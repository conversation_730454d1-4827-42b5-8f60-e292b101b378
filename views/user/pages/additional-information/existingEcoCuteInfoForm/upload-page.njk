{% extends "layout/user/main_layout.njk" %}
{% import "shared/lp/featured-common-parts.njk" as commonParts %}
{% from "user/common/progress-bar-adding-information.njk" import ProgressBar %}
{% from "user/common/upload-identity-files.njk" import UploadIdentityFiles %}
{% from "user/pages/additional-information/existingEcoCuteInfoForm/components/upload-form.njk" import UploadAdditionalInformationForm %}
{% from "user/pages/additional-information/existingEcoCuteInfoForm/components/confirm-information.njk" import ConfirmAdditionalInformation %}
{% from "user/common/upload-additional-files.njk" import UploadAdditionalFiles %}

{% set title = "給湯省エネ2025事業申請フォーム" %}
{% set ReEnableButtons_selector = "button:not(#js-submit-button), input" %}
{% set breadcrumbTitle = "給湯省エネ2025事業申請フォーム" %}

{% block head %}
  {% if _csrf %}
    <meta name="_csrf" id="_csrf" content="{{ _csrf }}">
  {% endif %}
  <script src="{{ assets.url("vendor/js/petite-vue.js") }}"></script>
  {% include "../../../../assets/dayjs.njk" %}
  <script src="{{ assets.url("js/date-format-utils.js") }}"></script>
{% endblock %}

{% block styles %}
  <link rel="stylesheet" href="{{ static.vendor("angular-ui-bootstrap/dist/ui-bootstrap-csp.css") }}"/>
  <link rel="stylesheet" href="{{ static.legacy("static/sp/css/curama-datepicker-theme.css") }}">
  {% if usingCalendarApp %}
    <link rel="stylesheet" href="{{ static.legacy("static/sp/css/calendar_user.css") }}">
  {% endif %}
  <link href="{{ assets.url("css/user/styles.css") }}" rel="stylesheet">
{% endblock %}

{% block scripts %}
  <script src="{{ static.legacy("common/js/jquery-ui.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/jquery.ui.datepicker-ja.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios.min.js") }}"></script>
  <script src="{{ assets.url("js/compressor.min.js") }}"></script>
  <script src="{{ static.legacy("static/sp/js/common.js") }}"></script>
  <script src="{{ assets.url("vendor/js/axios-client.js") }}" csrf="{{_csrf | safe}}"></script>
{% endblock %}

{% block body %}
  {% set combined = {
    user: user,
    userId: userId,
    usingApp: usingApp,
    packType: packType,
    templateType: templateType,
    addingInformationThankPageUrl: addingInformationThankPageUrl,
    additionalInformationAccountType: AdditionalInformationAccountType,
    verificationToken: verificationToken,
    isMobile: req.isMobile()
  } %}
  {{ security.renderTemplate("initialData", combined | dump) }}
  {{ ProgressBar() }}
  {{ UploadAdditionalInformationForm() }}
  {{ ConfirmAdditionalInformation() }}
  {{ UploadIdentityFiles() }}
  {{ UploadAdditionalFiles() }}

  {% raw %}
    <div v-scope="UploadAdditionalInformation()" @vue:mounted="mounted" v-cloak class="bac-c_bla02 mar-b_16 pos_r">
      <div v-if="isLoading" class="reservation-loading-wrap">
        <div class="sl-loading">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <div v-if="currentStep === 1" v-scope="UploadAdditionalInformationForm()"></div>
      <div v-if="currentStep === 2" v-scope="ConfirmAdditionalInformation()"></div>
    </div>
  {% endraw %}
  <script>
    const initialData = {{ security.getTemplateContent("initialData") }}
    const activePackages = initialData.activePackages;
    function UploadAdditionalInformation(props) {
      return {
        numberWithCommas: {{numberWithCommas|safe}},
        pdfIcon: "{{ assets.url('image/common/pdf-icon.png') }}",
        imgEcoCute1Src: "{{ assets.url('image/user/additional-information/existingEcoCuteInfoForm/img_ecocute1.png') }}",
        imgEcoCute2Src: "{{ assets.url('image/user/additional-information/existingEcoCuteInfoForm/img_ecocute2.png') }}",
        currentStep: 1,
        maxEcoCuteSlots: 5,
        isLoading: false,
        cacheKey: "ExistingEcoCuteInfoForm",
        addingInformationThankPageUrl: initialData.addingInformationThankPageUrl,
        listAccountType: initialData.additionalInformationAccountType,
        inputFiles: [],
        ecoCuteAttachments: [
          {
            nameplate: {
              name: "",
              file: null,
              url: "",
              type: 1,
            },
            fullView: {
              name: "",
              file: null,
              url: "",
              type: 1,
            },
          }
        ],
        forms: {},

        errors: {
          ecoCuteAttachments: [""],
        },

        requiredFields: [],

        defaultFieldKeyMapping: {
          default: "このフィールド"
        },

        getSubmitErrors() {
          const errors = [];
          Object.values(this.errors).forEach(error => {
            if (Array.isArray(error)) {
              errors.push(...error.filter(e => !!e));
            } else if (error) {
              errors.push(error);
            }
          });
          return errors;
        },

        mounted() {
          window.addEventListener('pageshow', (event) => {
            if (event.persisted) {
              this.isLoading = true;
              location.reload();
            }
          });
          window.onbeforeunload = function (e) {
            return e.returnValue = 'まだ登録が完了していません。\nこのページを離れると、入力した内容は破棄されます。';
          };
          const cachedData = sessionStorage.getItem(this.cacheKey);
          const { timestamp, forms } = cachedData
            ? JSON.parse(cachedData)
            : {};
          const cachedForm = forms || {};
          if (!timestamp || !forms)
            return
          if ((new Date().getTime() - timestamp) < 1000 * 60 * 30) {
            this.forms = {
              ...this.forms,
              ...cachedForm
            };
          } else {
            sessionStorage.removeItem(this.cacheKey);
          }
        },
      }
    };

    PetiteVue
      .createApp({UploadAdditionalInformation})
      .mount();
  </script>
  {{ commonParts.spBreadCrumb(breadcrumbTitle) }}
{% endblock %}

{% block tracking %}{% endblock %}